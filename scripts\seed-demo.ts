import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function seedDemo() {
  try {
    console.log('🌱 Seeding demo data...');

    // Create a demo user
    const user = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        name: 'Demo Organizer',
      },
    });

    // Create a demo event
    const event = await prisma.event.upsert({
      where: { slug: 'ux-summit-2024' },
      update: {},
      create: {
        name: 'UX Summit 2024',
        description: 'A collaborative conference exploring the future of user experience design',
        date: new Date('2024-03-15'),
        deadline: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
        slug: 'ux-summit-2024',
        isPremium: true,
        isPublished: true,
        creatorId: user.id,
      },
    });

    // Create demo notes
    const demoNotes = [
      {
        content: "The keynote on AI-driven design was fascinating. The speaker emphasized how AI can augment human creativity rather than replace it. Key tools mentioned: Figma AI, Midjourney for concept exploration, and GPT for copy generation. The future seems to be about human-AI collaboration.",
        author<PERSON><PERSON>: "<PERSON>",
        author<PERSON>mail: "<EMAIL>",
        standoutThought: "AI won't replace designers, but designers who use AI will replace those who don't.",
      },
      {
        content: "Accessibility workshop was eye-opening. Learned about WCAG 2.1 AA standards and how to implement them. Screen reader testing revealed so many issues I never considered. Need to integrate accessibility testing into our design process from day one, not as an afterthought.",
        authorName: "Marcus Johnson",
        authorEmail: "<EMAIL>",
        standoutThought: "Accessibility is not a feature, it's a fundamental human right in digital spaces.",
      },
      {
        content: "Design systems panel discussion highlighted the importance of governance. Companies with successful design systems have dedicated teams and clear processes. Token-based design systems are becoming the standard. Figma variables and design tokens are game-changers.",
        authorName: "Elena Rodriguez",
        standoutThought: "A design system without governance is just a collection of components.",
      },
      {
        content: "User research methods are evolving. Remote testing tools have improved dramatically. Unmoderated testing gives more natural behavior. Mixed methods approach (quant + qual) provides the most insights. Always validate assumptions with real users.",
        authorName: "David Kim",
        authorEmail: "<EMAIL>",
      },
      {
        content: "The ethics in design talk was thought-provoking. Dark patterns are everywhere and we need to be more conscious about the psychological impact of our designs. Persuasive design vs manipulative design - where do we draw the line?",
        authorName: "Anonymous",
        standoutThought: "With great design power comes great responsibility.",
      },
    ];

    for (const noteData of demoNotes) {
      await prisma.note.create({
        data: {
          ...noteData,
          eventId: event.id,
        },
      });
    }

    // Create demo attendees
    const attendeeEmails = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
    ];

    for (const email of attendeeEmails) {
      await prisma.attendee.upsert({
        where: {
          email_eventId: {
            email,
            eventId: event.id,
          },
        },
        update: {},
        create: {
          email,
          eventId: event.id,
        },
      });
    }

    console.log('✅ Demo data seeded successfully!');
    console.log(`📝 Event URL: http://localhost:3000/events/${event.slug}`);
    console.log(`👥 ${demoNotes.length} notes created`);
    console.log(`📧 ${attendeeEmails.length} attendees added`);

  } catch (error) {
    console.error('❌ Error seeding demo data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

seedDemo();
