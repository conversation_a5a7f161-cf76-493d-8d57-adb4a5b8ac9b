import { NextRequest, NextResponse } from "next/server";
import { extractTextFromImage, validateImageFile } from "@/lib/ocr";

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('image') as File;

    if (!file) {
      return NextResponse.json(
        { success: false, error: "No image file provided" },
        { status: 400 }
      );
    }

    // Validate the image file
    const validation = validateImageFile(file);
    if (!validation.isValid) {
      return NextResponse.json(
        { success: false, error: validation.error },
        { status: 400 }
      );
    }

    // Extract text using OCR
    const ocrResult = await extractTextFromImage(file);

    if (!ocrResult.text || ocrResult.text.length < 10) {
      return NextResponse.json(
        { 
          success: false, 
          error: "Could not extract readable text from the image. Please ensure the image is clear and contains text." 
        },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      text: ocrResult.text,
      confidence: ocrResult.confidence,
    });

  } catch (error) {
    console.error("OCR processing error:", error);
    return NextResponse.json(
      { success: false, error: "Failed to process image" },
      { status: 500 }
    );
  }
}
