import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { db } from "@/lib/db";

const submitNoteSchema = z.object({
  content: z.string().min(1, "Notes content is required"),
  authorName: z.string().optional(),
  authorEmail: z.string().email().optional(),
  standoutThought: z.string().optional(),
  originalText: z.string().optional(), // For OCR original text
});

export async function POST(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const body = await request.json();
    const validatedData = submitNoteSchema.parse(body);

    // Find the event
    const event = await db.event.findUnique({
      where: {
        slug: params.slug,
        isPublished: true,
      }
    });

    if (!event) {
      return NextResponse.json(
        { success: false, error: "Event not found" },
        { status: 404 }
      );
    }

    // Check if deadline has passed
    if (new Date() > event.deadline) {
      return NextResponse.json(
        { success: false, error: "Submission deadline has passed" },
        { status: 400 }
      );
    }

    // Create the note
    const note = await db.note.create({
      data: {
        content: validatedData.content,
        authorName: validatedData.authorName,
        authorEmail: validatedData.authorEmail,
        standoutThought: validatedData.standoutThought,
        originalText: validatedData.originalText,
        eventId: event.id,
      }
    });

    // If this is a premium event and email is provided, add to attendees
    if (event.isPremium && validatedData.authorEmail) {
      await db.attendee.upsert({
        where: {
          email_eventId: {
            email: validatedData.authorEmail,
            eventId: event.id,
          }
        },
        update: {
          name: validatedData.authorName,
        },
        create: {
          email: validatedData.authorEmail,
          name: validatedData.authorName,
          eventId: event.id,
        }
      });
    }

    return NextResponse.json({
      success: true,
      note: {
        id: note.id,
        createdAt: note.createdAt,
      }
    });

  } catch (error) {
    console.error("Error submitting note:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: "Validation error", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    // Find the event
    const event = await db.event.findUnique({
      where: {
        slug: params.slug,
        isPublished: true,
      }
    });

    if (!event) {
      return NextResponse.json(
        { success: false, error: "Event not found" },
        { status: 404 }
      );
    }

    // Get notes count and basic stats (don't expose actual content for privacy)
    const notesCount = await db.note.count({
      where: {
        eventId: event.id,
      }
    });

    const recentSubmissions = await db.note.findMany({
      where: {
        eventId: event.id,
      },
      select: {
        id: true,
        authorName: true,
        createdAt: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
      take: 5,
    });

    return NextResponse.json({
      success: true,
      stats: {
        totalNotes: notesCount,
        recentSubmissions: recentSubmissions.map(note => ({
          id: note.id,
          authorName: note.authorName || 'Anonymous',
          createdAt: note.createdAt,
        })),
      }
    });

  } catch (error) {
    console.error("Error fetching notes stats:", error);
    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    );
  }
}
