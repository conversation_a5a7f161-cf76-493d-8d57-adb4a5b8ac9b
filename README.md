# 🎯 Mosaipad - Collaborative Event Note Summarization Platform

Transform everyone's notes into one collective mosaic! Mosaipad is a Next.js platform that allows event attendees to upload notes (text or images) and automatically generates beautiful, structured collaborative summaries using AI.

## ✨ Features

### 🧩 Core Features
- **Event Creation**: Simple form to set up events with deadlines and branding
- **Note Collection**: Text input and image upload (with OCR support)
- **AI Summarization**: Smart clustering and synthesis using OpenAI GPT-4
- **Beautiful Output**: Professional PDFs and web summaries
- **Real-time Updates**: Live submission counts and progress tracking

### 🔐 Security & Privacy
- Input validation and sanitization
- File type and size restrictions
- Privacy-first note processing
- Secure API endpoints with rate limiting

### 💎 Premium Features
- Email invitations and notifications
- Custom branding and logos
- Advanced analytics
- Automated summary delivery

## 🚀 Tech Stack

- **Frontend**: Next.js 15, React 19, TypeScript
- **Styling**: Tailwind CSS 4, Radix UI components
- **Database**: Prisma ORM with PostgreSQL
- **AI**: OpenAI GPT-4 for summarization
- **OCR**: Tesseract.js for image-to-text conversion
- **Authentication**: NextAuth.js (ready for implementation)
- **Email**: Resend API integration
- **Deployment**: Vercel-optimized

## 🛠️ Getting Started

### Prerequisites
- Node.js 18+
- PostgreSQL database
- OpenAI API key
- Resend API key (for email features)

### Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd mosaipad
```

2. **Install dependencies**
```bash
npm install
```

3. **Set up environment variables**
Copy `.env.local` and fill in your values:
```bash
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/mosaipad"

# NextAuth.js
NEXTAUTH_SECRET="your-secret-key-here"
NEXTAUTH_URL="http://localhost:3000"

# OpenAI API
OPENAI_API_KEY="your-openai-api-key-here"

# Email Service (Resend)
RESEND_API_KEY="your-resend-api-key-here"

# App Configuration
APP_URL="http://localhost:3000"
APP_NAME="Mosaipad"
```

4. **Set up the database**
```bash
npx prisma generate
npx prisma db push
```

5. **Run the development server**
```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to see the application.

## 📁 Project Structure

```
src/
├── app/                    # Next.js App Router
│   ├── api/               # API routes
│   │   ├── events/        # Event management
│   │   ├── ocr/          # Image processing
│   │   └── auth/         # Authentication (future)
│   ├── create-event/     # Event creation page
│   ├── events/           # Event pages
│   │   └── [slug]/       # Dynamic event routes
│   └── globals.css       # Global styles
├── components/
│   └── ui/               # Reusable UI components
├── lib/
│   ├── db.ts            # Database client
│   ├── utils.ts         # Utility functions
│   ├── ocr.ts           # OCR processing
│   └── ai-summarizer.ts # AI summarization
└── prisma/
    └── schema.prisma    # Database schema
```

## 🎨 Design System

### Color Palette
- **Primary**: Indigo 600 (Trust, depth, professionalism)
- **Secondary**: Sky Blue 400 (Clarity, digital-first feel)
- **Accent**: Emerald Green 500 (Growth, community vibe)
- **Neutral**: Soft whites and grays for breathing space

### Typography
- **Primary Font**: Inter (Clean, modern, international)
- **Fallbacks**: Roboto, Open Sans, system fonts

### UI Principles
- **Mobile-first**: Responsive design for all screen sizes
- **Accessibility**: WCAG 2.1 AA compliance
- **Performance**: Optimized images and lazy loading
- **User Experience**: Clear navigation and feedback

## 🔄 User Flows

### Event Creator Flow
1. **Create Event** → Fill form with event details
2. **Get Shareable Link** → Copy/share event URL
3. **Monitor Submissions** → View real-time stats
4. **Generate Summary** → AI processes notes after deadline
5. **Download/Share** → Get PDF or web summary

### Attendee Flow
1. **Receive Link** → From organizer or email
2. **Submit Notes** → Text or image upload
3. **Get Confirmation** → Success message with timeline
4. **Receive Summary** → Email notification when ready

### AI Processing Flow
1. **Collect Notes** → Gather all submissions
2. **Clean & Process** → OCR, validation, privacy filtering
3. **Cluster & Analyze** → Group similar ideas, find outliers
4. **Generate Summary** → Structured output with sections
5. **Create Outputs** → HTML page and PDF document

## 🚀 Deployment

### Vercel (Recommended)
1. Connect your GitHub repository to Vercel
2. Set environment variables in Vercel dashboard
3. Deploy automatically on push to main branch

### Environment Variables for Production
```bash
DATABASE_URL="your-production-database-url"
NEXTAUTH_SECRET="your-production-secret"
NEXTAUTH_URL="https://your-domain.com"
OPENAI_API_KEY="your-openai-api-key"
RESEND_API_KEY="your-resend-api-key"
APP_URL="https://your-domain.com"
```

## 🧪 Testing

```bash
# Run type checking
npm run type-check

# Run linting
npm run lint

# Build for production
npm run build
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **OpenAI** for GPT-4 API
- **Vercel** for Next.js and hosting
- **Tailwind CSS** for styling system
- **Prisma** for database ORM
- **Tesseract.js** for OCR capabilities

---

**Built with ❤️ by the Mosaipad Team**

Transform everyone's notes into collective insights! 🎯
