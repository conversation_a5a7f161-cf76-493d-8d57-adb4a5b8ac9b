// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id            String    @id @default(cuid())
  name          String?
  email         String    @unique
  emailVerified DateTime?
  image         String?
  role          UserRole  @default(USER)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  accounts Account[]
  sessions Session[]
  events   Event[]
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model Event {
  id          String      @id @default(cuid())
  name        String
  description String?
  date        DateTime?
  deadline    DateTime
  slug        String      @unique
  logoUrl     String?
  sponsorMessage String?
  isPremium   Boolean     @default(false)
  isPublished Boolean     @default(false)
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  
  creatorId   String
  creator     User        @relation(fields: [creatorId], references: [id], onDelete: Cascade)
  
  notes       Note[]
  attendees   Attendee[]
  summary     Summary?
}

model Attendee {
  id        String   @id @default(cuid())
  email     String
  name      String?
  eventId   String
  createdAt DateTime @default(now())
  
  event     Event    @relation(fields: [eventId], references: [id], onDelete: Cascade)
  
  @@unique([email, eventId])
}

model Note {
  id          String     @id @default(cuid())
  content     String     @db.Text
  originalText String?   @db.Text // For OCR original text
  authorName  String?
  authorEmail String?
  standoutThought String? @db.Text
  imageUrl    String?
  isProcessed Boolean    @default(false)
  createdAt   DateTime   @default(now())
  
  eventId     String
  event       Event      @relation(fields: [eventId], references: [id], onDelete: Cascade)
}

model Summary {
  id              String   @id @default(cuid())
  content         String   @db.Text
  htmlContent     String   @db.Text
  pdfUrl          String?
  keyTakeaways    String[] @db.Text
  standoutThoughts String[] @db.Text
  unexpectedNuggets String[] @db.Text
  questionsReflections String[] @db.Text
  mentions        String[] @db.Text
  communityQuotes String[] @db.Text
  isGenerated     Boolean  @default(false)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  
  eventId         String   @unique
  event           Event    @relation(fields: [eventId], references: [id], onDelete: Cascade)
}

enum UserRole {
  USER
  ADMIN
}
